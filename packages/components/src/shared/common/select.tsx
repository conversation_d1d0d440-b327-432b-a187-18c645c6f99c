import { SelectorVerticalIcon } from '@mass/icons'
import type { BlobType } from '@mass/utils'
import { Arrow, Content, Icon, Item, ItemText, Portal, Root, Trigger, Value, Viewport } from '@radix-ui/react-select'
import clsx from 'clsx'
import { type FC, useState } from 'react'

import { useButtonStyles } from './button'

export interface SelectOption {
  value: string
  label: string
  disabled?: boolean
}

export const Select: FC<{
  placeholder?: string
  options: SelectOption[]
  onValueChange?: (value: BlobType) => void
  value?: string
  defaultValue?: string
  disabled?: boolean
  children?: React.ReactNode
  className?: string
  defaultOpen?: boolean
}> = ({
  placeholder = 'Select an option...',
  options,
  defaultValue,
  onValueChange,
  value,
  disabled,
  className,
  defaultOpen = false,
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen)
  const selectedOption = options.find(option => option.value === value)

  const rootProps = {
    ...(onValueChange && { onValueChange }),
    ...(value && { value }),
    ...(defaultValue && { defaultValue }),
    ...(disabled !== undefined && { disabled }),
    open: isOpen,
    onOpenChange: setIsOpen,
    ...props,
  }

  return (
    <Root {...rootProps}>
      <Trigger
        className={clsx(
          useButtonStyles({ variant: 'bordered' }), // variants
          'justify-between! bg-white', // styles
          'text-nowrap',
          className,
        )}>
        <Value placeholder={placeholder} className='text-nowrap'>
          {selectedOption?.label}
        </Value>
        <Icon>
          <SelectorVerticalIcon
            className={clsx(
              'h-8 w-8 text-dim-3', // sizing
              'transition-transform duration-300', // animation
              {
                '-rotate-90': isOpen,
              },
            )}
          />
        </Icon>
      </Trigger>

      <Portal>
        <Content
          className={clsx(
            'relative z-[9999] overflow-hidden',
            'min-w-[200px]',
            'rounded-b1 bg-white shadow-layer-1',
            'border border-accessory-1',
          )}
          position='popper'
          side='bottom'
          align='center'
          sideOffset={4}>
          <Viewport>
            {options.map(option => (
              <Item
                key={option.value}
                value={option.value as BlobType}
                disabled={option.disabled ?? false}
                className={clsx(
                  'relative', // positioning
                  'flex items-center justify-between', // flex
                  'px-8 py-3', // spacing
                  'text-black text-xs',
                  'cursor-pointer select-none outline-none transition-colors duration-200', // other
                  {
                    'hover:bg-black/5 focus:bg-black/5': !option.disabled,
                    'cursor-not-allowed opacity-50': option.disabled,
                  },
                )}>
                {option.value === value && <span className='absolute inset-y-0 left-0 w-2 bg-primary' />}
                <ItemText>{option.label}</ItemText>
              </Item>
            ))}
          </Viewport>
          <Arrow />
        </Content>
      </Portal>
    </Root>
  )
}
