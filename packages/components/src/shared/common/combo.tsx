import { Combobox, ComboboxButton, ComboboxInput, ComboboxOption, ComboboxOptions } from '@headlessui/react'
import { SelectorVerticalIcon } from '@mass/icons'
import type { BlobType } from '@mass/utils'
import clsx from 'clsx'
import { type FC, useRef, useState } from 'react'

import { useButtonStyles } from './button'

export interface ComboOption {
  value: string
  label: string
  disabled?: boolean
}

export const Combo: FC<{
  placeholder?: string
  options: ComboOption[]
  onValueChange?: (value: BlobType) => void
  value?: string
  defaultValue?: string
  disabled?: boolean
  className?: string
  searchable?: boolean
  emptyMessage?: string
  defaultOpen?: boolean
  filterFunction?: (options: ComboOption[], query: string) => ComboOption[]
  customSort?: (query: string, a: ComboOption, b: ComboOption) => number
}> = ({
  placeholder = 'Select an option...',
  options,
  onValueChange,
  value,
  defaultValue,
  disabled,
  className,
  searchable = true,
  emptyMessage = 'No options found.',
  defaultOpen = false,
  filterFunction,
  customSort,
  ...props
}) => {
  const [query, setQuery] = useState('')
  const buttonRef = useRef<HTMLButtonElement>(null)

  // Default filter function
  const defaultFilter = (optionsList: ComboOption[], searchQuery: string) => {
    if (searchQuery === '') {
      return optionsList
    }
    return optionsList.filter(option => option.label.toLowerCase().includes(searchQuery.toLowerCase()))
  }

  const filteredOptions = filterFunction ? filterFunction(options, query) : defaultFilter(options, query)
  const sortedOptions = customSort ? filteredOptions.sort((a, b) => customSort(query, a, b)) : filteredOptions

  const handleChange = (selectedValue: string | null) => {
    if (selectedValue !== null && onValueChange) {
      onValueChange(selectedValue as BlobType)
    }
  }

  return (
    <Combobox
      immediate
      value={value || defaultValue || null}
      onChange={handleChange}
      disabled={disabled ?? false}
      {...props}>
      {({ open }) => (
        <div className={clsx('relative w-full', className)}>
          <ComboboxInput
            className={clsx(
              // biome-ignore lint/correctness/useHookAtTopLevel: Redundant
              useButtonStyles({ variant: 'bordered' }), // variants
              'justify-between! bg-white', // styles
              'text-nowrap pr-16', // padding for icon
              {
                'cursor-not-allowed': disabled,
              },
            )}
            displayValue={(option: string) => {
              const foundOption = options.find(opt => opt.value === option)
              return foundOption?.label || ''
            }}
            onChange={event => searchable && setQuery(event.target.value)}
            placeholder={placeholder}
            readOnly={!searchable}
          />

          <ComboboxButton ref={buttonRef} className='absolute inset-y-0 right-2 flex items-center px-6'>
            <SelectorVerticalIcon
              className={clsx(
                'h-8 w-8 text-dim-3', // sizing
                'transition-transform duration-300', // animation
                {
                  'rotate-90': open,
                },
              )}
            />
          </ComboboxButton>

          <ComboboxOptions
            className={clsx(
              'absolute z-[9999] mt-1 max-h-60 w-full overflow-auto',
              'rounded-b1 bg-white shadow-layer-1',
              'border border-accessory-1',
              'scrollbar-b1 py-1',
            )}>
            {sortedOptions.length === 0 ? (
              <div className='relative cursor-default select-none px-8 py-3 text-dim-3 text-xs'>{emptyMessage}</div>
            ) : (
              sortedOptions.map(option => (
                <ComboboxOption
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled ?? false}
                  className={({ focus }) =>
                    clsx(
                      'relative', // positioning
                      'flex items-center justify-between', // flex
                      'px-8 py-3', // spacing
                      'text-black text-xs',
                      'cursor-pointer select-none outline-none transition-colors duration-200', // other
                      {
                        'bg-black/5': focus && !option.disabled,
                        'cursor-not-allowed opacity-50': option.disabled,
                      },
                    )
                  }>
                  {({ selected }) => (
                    <>
                      {selected && <span className='absolute inset-y-0 left-0 w-2 bg-primary' />}
                      <span className={clsx('block truncate', { 'font-medium': selected })}>{option.label}</span>
                    </>
                  )}
                </ComboboxOption>
              ))
            )}
          </ComboboxOptions>
        </div>
      )}
    </Combobox>
  )
}
