import { Combobox, ComboboxButton, ComboboxInput, ComboboxOption, ComboboxOptions } from '@headlessui/react'
import { SelectorVerticalIcon } from '@mass/icons'
import type { BlobType } from '@mass/utils'
import clsx from 'clsx'
import { type FC, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useButtonStyles } from './button'

export interface ComboOption {
  value: string
  label: string
  disabled?: boolean
}

export const Combo: FC<{
  options: ComboOption[]
  onValueChange?: (value: BlobType) => void
  value?: string
  defaultValue?: string
  disabled?: boolean
  className?: string
  searchable?: boolean
  customSort?: (query: string, a: ComboOption, b: ComboOption) => number
}> = ({
  options,
  onValueChange,
  value,
  defaultValue,
  disabled,
  className,
  searchable = true,
  customSort,
  ...props
}) => {
  const [query, setQuery] = useState('')
  const buttonStyles = useButtonStyles({ variant: 'bordered' })
  const { t: common } = useTranslation('common')

  const filteredOptions =
    query === '' ? options : options.filter(option => option.label.toLowerCase().includes(query.toLowerCase()))

  if (customSort) {
    filteredOptions.sort((a, b) => customSort(query, a, b))
  }

  const handleChange = (selectedValue: string | null) => {
    if (selectedValue && onValueChange) {
      onValueChange(selectedValue as BlobType)
    }
  }

  return (
    <Combobox
      immediate
      value={value || defaultValue || null}
      onChange={handleChange}
      disabled={disabled ?? false}
      {...props}>
      {({ open }) => (
        <div className={clsx('relative w-full', className)}>
          <ComboboxInput
            className={clsx(
              buttonStyles, // variants
              'justify-between! bg-white', // styles
              'text-nowrap pr-23', // padding for icon
              {
                'cursor-not-allowed': disabled,
              },
            )}
            displayValue={(option: string) => {
              const foundOption = options.find(opt => opt.value === option)
              return foundOption?.label || ''
            }}
            onChange={event => searchable && setQuery(event.target.value)}
            placeholder={common('select-an-option')}
            readOnly={!searchable}
            autoComplete='off'
          />

          <ComboboxButton className='absolute inset-y-0 right-2 flex cursor-pointer items-center px-6'>
            <SelectorVerticalIcon
              className={clsx(
                'h-8 w-8 text-dim-3', // sizing
                'transition-transform duration-300', // animation
                {
                  '-rotate-90': open,
                },
              )}
            />
          </ComboboxButton>

          <ComboboxOptions
            className={clsx(
              'absolute z-[9999] mt-4 max-h-60 w-full overflow-auto',
              'rounded-b1 bg-white shadow-layer-1',
              'border border-accessory-1',
              'scrollbar-b1 py-1',
            )}>
            {filteredOptions.length === 0 ? (
              <div className='relative cursor-default select-none px-8 py-3 text-dim-3 text-xs'>
                {common('no-results')}
              </div>
            ) : (
              filteredOptions.map(option => (
                <ComboboxOption
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled ?? false}
                  className={({ focus }) =>
                    clsx(
                      'relative', // positioning
                      'flex items-center justify-between', // flex
                      'px-8 py-3', // spacing
                      'text-black text-xs',
                      'cursor-pointer select-none outline-none transition-colors duration-200', // other
                      {
                        'bg-black/5': focus && !option.disabled,
                        'cursor-not-allowed opacity-50': option.disabled,
                      },
                    )
                  }>
                  {({ selected }) => (
                    <>
                      {selected && <span className='absolute inset-y-0 left-0 w-2 bg-primary' />}
                      <span className={clsx('block truncate', { 'font-medium': selected })}>{option.label}</span>
                    </>
                  )}
                </ComboboxOption>
              ))
            )}
          </ComboboxOptions>
        </div>
      )}
    </Combobox>
  )
}
