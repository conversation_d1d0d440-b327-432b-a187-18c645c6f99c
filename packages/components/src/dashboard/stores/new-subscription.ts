import { observable } from '@legendapp/state'
import type { BlobType } from '@mass/utils'

export const newSubscription$ = observable<Dashboard.NewSubscription>({
  payload: {},

  clearPayload: () => {
    newSubscription$.payload.set({})
  },

  use: (type): BlobType => {
    return {
      value: newSubscription$.payload[type].get(),
      set: (value: BlobType) => {
        newSubscription$.payload.set(prev => {
          prev[type] = value

          return prev
        })
      },
    }
  },
})
