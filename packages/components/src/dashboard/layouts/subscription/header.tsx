import { Memo } from '@legendapp/state/react'
import { global$ } from '@mass/api'
import { FilterLinesIcon, PlusIcon } from '@mass/icons'
import { useLocation } from '@tanstack/react-router'
import clsx from 'clsx'
import type { FC } from 'react'
import { useTranslation } from 'react-i18next'

import { Badge, Button, Text, Title, ui$ } from '../../../shared'
import { useMeta } from '../../hooks/use-meta'
import { Breadcrumbs } from '../shared/breadcrumbs'

const HomeHeaderActions: FC = () => {
  const { t: common } = useTranslation('common')

  return (
    <div className='flex gap-4'>
      <Button
        variant='bordered'
        className='rounded-c1'
        onClick={() => ui$.onChangeModal('dashboard.subscription-filters', true)}>
        <Text variant='dim-2-medium'> {common('filter')} </Text>
        <FilterLinesIcon strokeWidth={1.5} />
      </Button>

      <Button
        variant='primary'
        className='rounded-c1'
        onClick={() => ui$.onChangeModal('dashboard.new-subscription', true)}>
        <Text variant='white' className='text-nowrap'>
          {common('add-new')}
        </Text>
        <PlusIcon strokeWidth={1.5} />
      </Button>
    </div>
  )
}

const HeaderActions: FC = () => {
  const { pathname } = useLocation()

  if (pathname === '/') {
    return <HomeHeaderActions />
  }

  return null
}

export const Header: FC = () => {
  const { pathname } = useLocation()
  const { t: dashboard } = useTranslation('dashboard')

  const { title, description, hasTabs } = useMeta()

  const showBadge = pathname === '/'

  return (
    <header
      className={clsx(
        'flex flex-col gap-16', // flex
        'h-auto w-full max-w-screen', // sizing
        'px-16 pt-16', // padding
        'border-accessory-1 border-b', // accessory
        {
          'pb-16': !hasTabs,
        },
      )}>
      <Breadcrumbs />

      <div
        className={clsx(
          'flex flex-row items-end justify-between gap-4', // flex
        )}>
        <div
          className={clsx(
            'flex flex-row', // flex
          )}>
          <div
            className={clsx(
              'flex flex-col gap-4', // flex
            )}>
            <div className='flex items-center gap-8'>
              <Title> {title} </Title>
              {showBadge && (
                <Badge slim>
                  <Memo>
                    {() =>
                      dashboard('subscriptions.x-many-subscriptions', {
                        count: global$.subscription.subscriptions.get()?.content.length ?? 0,
                      })
                    }
                  </Memo>
                </Badge>
              )}
            </div>
            <Text variant='dim-2'> {description} </Text>
          </div>
        </div>

        <HeaderActions />
      </div>
    </header>
  )
}
