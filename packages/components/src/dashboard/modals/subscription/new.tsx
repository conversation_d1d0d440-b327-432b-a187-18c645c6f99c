import { PlusIcon } from '@mass/icons'
import type { FC } from 'react'
import { toast } from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  ui$,
} from '../../../shared'
import { subscriptionFilters$ } from '../../stores/subscription-filters'
import { newSubscription$ } from '../../stores/new-subscription'
import { use$ } from '@legendapp/state/react'

export const NewSubscriptionModal: FC = () => {
  const { t: dashboard } = useTranslation('dashboard')
  const { t: common } = useTranslation('common')

  const name = use$(() => newSubscription$.use('name').value)
  const individual = use$(() => newSubscription$.use('individual').value)
  const personIdentifier = use$(() => newSubscription$.use('personIdentifier').value)
  const regionId = use$(() => newSubscription$.use('regionId').value)
  const installationId = use$(() => newSubscription$.use('installationId').value)

  const handleCancel = () => {
    subscriptionFilters$.clearFilters()
    ui$.onChangeModal('dashboard.new-subscription', false)

    toast.success(common('filters-cleared'))
  }

  // biome-ignore lint/suspicious/useAwait: <explanation>
  const handleCreate = async () => {
    try {
      ui$.onChangeModal('dashboard.new-subscription', false)

      toast.success(common('filters-applied'))
    } catch (err) {
      toast.error(common('something-went-wrong'))
    }
  }

  return (
    <DialogRoot name='dashboard.new-subscription'>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent
          className='sm:max-h-[400px]! sm:max-w-[450px]'
          header={
            <DialogHeader
              icon={<PlusIcon strokeWidth={2} className='h-12 w-12' />}
              title={dashboard('subscriptions.new')}
              description={dashboard('subscriptions.new-description')}
            />
          }
          footer={
            <DialogFooter slim={false} borderLess className='flex-row gap-4'>
              <Button variant='bordered' onClick={handleCancel}>
                {common('cancel')}
              </Button>
              <Button variant='primary' onClick={handleCreate}>
                {common('add')}
              </Button>
            </DialogFooter>
          }>
          
          
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
}
