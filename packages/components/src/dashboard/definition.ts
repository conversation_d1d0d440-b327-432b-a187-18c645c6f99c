import type { ArrayValue } from '@legendapp/state'

declare global {
  namespace Dashboard {
    type Modals =
      | 'subscription-filters'
      | 'add-new-subscription'
      | `document-${keyof Api.Stores.Global['aggreements']}`
      | 'test'

    type SubscriptionParams = Api.ExtractParams<Api.Services['subscriptions']['subscriptions']>

    interface SubscriptionFilters {
      params: SubscriptionParams

      clearFilters: () => void

      eq: <
        T extends keyof ArrayValue<SubscriptionParams['filter:eq']>,
        const D,
        R = ArrayValue<SubscriptionParams['filter:eq']>[T] | D,
      >(
        type: T,
        options: {
          defaultValue: D
          removeIfDefault?: boolean
        },
      ) => {
        value: Exclude<R | D, undefined>
        set: (value: R) => void
      }
    }
  }
}
